# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/workers/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# Cloudflare Workers environment files
workers/.dev.vars
workers/.prod.vars
workers/.wrangler/

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/

# OS
Thumbs.db

# Data files
/data/
# Ignore data JSON files but not config files
/data/*.json
*.data.json

# Security - API keys and secrets
**/config/secrets.py
**/config/api_keys.py
**/*_secrets.py
**/*_keys.py
